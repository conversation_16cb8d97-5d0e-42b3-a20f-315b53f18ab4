<?php

namespace Modules\Cargo\Http\Controllers;

use App\Http\Resources\FlyersOrdersResource;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Http\DataTables\ClientsDataTable;
use Modules\Cargo\Http\DataTables\ClientAddressDataTable;
use Modules\Cargo\Http\Requests\ClientRequest;
use Modules\Cargo\Http\Requests\ClientAddressRequest;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\ClientStateCost;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Shipment;
use App\Models\User;
use Modules\Cargo\Http\Helpers\UserRegistrationHelper;
use Modules\Users\Events\UserCreatedEvent;
use Modules\Users\Events\UserUpdatedEvent;
use Modules\Cargo\Entities\Package;
use Modules\Cargo\Entities\ClientPackage;
use Modules\Cargo\Entities\ClientAddress;
use Modules\Cargo\Entities\State;
use Modules\Cargo\Http\Requests\AddressRequest;
use Modules\Cargo\Entities\BusinessSetting;
use app\Http\Helpers\ApiHelper;
use DB;
use Modules\Cargo\Events\AddClient;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Http\Requests\RegisterRequest;
use Auth;
use Illuminate\Support\Facades\Validator;
use Modules\Cargo\Entities\Flyer;
use Modules\Cargo\Entities\FlyerOrder;

class ClientController extends Controller
{
    private $aclRepo;

    public function __construct(AclRepository $aclRepository)
    {
        $this->aclRepo = $aclRepository;
        // check on permissions
        $this->middleware('user_role:1|0|3')->only('index','clientsReport');
        $this->middleware('user_role:1|0|3|4')->only('show');
        $this->middleware('user_role:1|0|3')->only('create', 'store');
        $this->middleware('user_role:1|0|3')->only('edit');
        $this->middleware('user_role:1|0|3|4')->only('update');
        $this->middleware('user_role:1|0|3')->only('delete', 'multiDestroy');
        $this->middleware('user_role:4')->only('profile');
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(ClientsDataTable $dataTable)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.clients')
            ]
        ]);
        $data_with = [];
        $share_data = array_merge(get_class_vars(ClientsDataTable::class), $data_with);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::'.$adminTheme.'.pages.clients.index', $share_data);
    }

    public function newAddress() {

        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.add_address'),
            ],
        ]);

        if(auth()->user()->role == 3){
            $branches = Branch::where('is_archived',0)->where('user_id',auth()->user()->id)->get();
        }else{
            $branches = Branch::where('is_archived',0)->get();
        }

        $client = Client::where('user_id', auth()->user()->id)->first();

        $adminTheme = env('ADMIN_THEME', 'adminLte');
            $branches = Branch::where('is_archived',0)->where('user_id',auth()->user()->id)->get();
        return view('cargo::'.$adminTheme.'.pages.clients.create_add_address')->with(['client'=>$client ,'branches'=>$branches]);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.clients'),
                'path' => fr_route('clients.index')
            ],
            [
                'name' => __('cargo::view.add_client'),
            ],
        ]);
        if(auth()->user()->role == 3){
            $branches = Branch::where('is_archived',0)->where('user_id',auth()->user()->id)->get();
        }else{
            $branches = Branch::where('is_archived',0)->get();
        }
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::'.$adminTheme.'.pages.clients.create')->with(['branches' => $branches]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(ClientRequest $request)
    {
        $data = $request->only([ 'state_id' , 'area_id' , 'payment_details' , 'def_mile_cost','def_return_mile_cost','def_mile_cost_gram','def_return_mile_cost_gram','def_return_cost_gram','def_insurance_gram','def_tax_gram','def_shipping_cost_gram','def_return_cost','def_insurance','def_tax','def_shipping_cost','supply_cost','pickup_cost','how_know_us','follow_up_mobile','follow_up_country_code' , 'follow_up_name','name', 'email', 'password', 'responsible_mobile', 'country_code' ,'responsible_name','national_id','branch_id','address']);

        $Userdata['name']     = $data['name'];
        $Userdata['email']    = $data['email'];

        if(isset($data['password'])){
            $Userdata['password'] = $data['password'];
        }
        $Userdata['role']     = 4;
        $userRegistrationHelper = new UserRegistrationHelper();
		$response = $userRegistrationHelper->NewUser($Userdata);
        if(!$response['success']){
            throw new \Exception($response['error_msg']);
        }

        $data['code']    = 0;
        $data['user_id'] = $response['user']['id'];
        $data['created_by'] = auth()->check() ? auth()->id() : null;
        unset($data['password']);
        unset($data['address']);

        $client = new Client();
        $client->fill($data);
        if (!$client->save()){
            throw new \Exception();
        }
        $client->code = $client->id;
        if (!$client->save()){
            throw new \Exception();
        }

        if (isset($request->package_id)) {
            if (!empty($request->package_id)) {

                foreach ($request->package_id as $key => $package) {
                    $client_package = new ClientPackage();

                    $client_package->client_id    = $client->id;
                    $client_package->package_id   = $package;
                    $client_package->name = Package::select('name')->where('id',$package)->first();
                    $client_package->name = $client_package->name->name;
                    $client_package->cost = $request->package_extra[$key];

                    if (!$client_package->save()) {
                        throw new \Exception();
                    }
                }
            }
        }

        if (isset($request->address) && !empty($request->address) ) {
            foreach ($request->address as $address) {

                if(isset($address['address']) && $address['address'] != null )
                {
                    $client_address = new ClientAddress();
                    $client_address->fill($address);
                    $client_address->client_id = $client->id;

                    if (!$client_address->save()) {
                        throw new \Exception();
                    }
                }
            }
        }

        $client->addFromMediaLibraryRequest($request->image)->toMediaCollection('avatar');
        event(new AddClient($client));
        return redirect()->route('clients.index')->with(['message_alert' => __('cargo::messages.created')]);

    }

    public function register()
    {
        $branches = Branch::where('is_archived',0)->get();
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::'.$adminTheme.'.pages.clients.register')->with(['branches' => $branches]);
    }

    public function registerStore(Request $request , $calc = false)
    {
        $countryCodeReq = 'required';
        if($calc){
            $countryCodeReq = 'nullable';
        }

        // Normalize phone number before validation
        if (isset($request->responsible_mobile)) {
            $request->merge([
                'responsible_mobile' => $this->normalizePhoneForClient($request->responsible_mobile)
            ]);
        }

        $request->validate([
            'name' => 'required|string|min:3|max:50',
            'email' => 'required|max:50|email|unique:users,email',
            'password' => 'required|string|min:6',
            'responsible_mobile' => 'required|min:7', // Made more flexible
            'country_code' => $countryCodeReq,
            'responsible_name' => 'required|string|min:3|max:50',
            'national_id'   => 'required',
            'branch_id' => 'required',
            'terms_conditions' => 'required',
        ]);

        $data = $request->only(['name', 'email', 'password', 'responsible_mobile', 'country_code' , 'responsible_name','national_id','branch_id']);

        $Userdata['name']     = $data['name'];
        $Userdata['email']    = $data['email'];
        $Userdata['password'] = $data['password'];
        $Userdata['role']     = 4;

        $userRegistrationHelper = new UserRegistrationHelper();
		$response = $userRegistrationHelper->NewUser($Userdata);
        if(!$response['success']){
            throw new \Exception($response['error_msg']);
        }

        $data['code']    = 0;
        $data['status']    = 'not_approved';
        $data['user_id'] = $response['user']['id'];
        $data['created_by'] = auth()->check() ? auth()->id() : null;
        unset($data['password']);
        unset($data['address']);

        $client = new Client();
        $client->fill($data);
        if (!$client->save()){
            throw new \Exception();
        }
        $client->code = $client->id;
        if (!$client->save()){
            throw new \Exception();
        }
        event(new AddClient($client));
        Auth::loginUsingId($client->user_id);

        if($calc)
        {
            return $client;
        }

        return redirect()->route('admin.dashboard');
    }

    /**
     * Show the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function show($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('view.profile_details')
            ],
        ]);
        $user = Client::findOrFail($id);
        $shipments = Shipment::where('client_id', $id)->count();
        $adminTheme = env('ADMIN_THEME', 'adminLte');return view('cargo::'.$adminTheme.'.pages.clients.show')->with(['model' => $user, 'shipments' => $shipments]);
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.edit_client'),
            ],
        ]);

        if(auth()->user()->role == 3){
            $branches = Branch::where('is_archived',0)->where('user_id',auth()->user()->id)->get();
        }else{
            $branches = Branch::where('is_archived',0)->get();
        }
        $client = Client::findOrFail($id);
        $packages = $client->packages;
        $adminTheme = env('ADMIN_THEME', 'adminLte');return view('cargo::'.$adminTheme.'.pages.clients.edit')->with(['model' => $client, 'branches' => $branches, 'packages' => $packages]);
    }

    public  function profile($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.edit_profile'),
            ],
        ]);

        if(auth()->user()->role == 3){
            $branches = Branch::where('is_archived',0)->where('user_id',auth()->user()->id)->get();
        }else{
            $branches = Branch::where('is_archived',0)->get();
        }


        $client = Client::findOrFail($id);
        $packages = $client->packages;
        $adminTheme = env('ADMIN_THEME', 'adminLte');return view('cargo::'.$adminTheme.'.pages.clients.edit-profile')->with(['model' => $client, 'branches' => $branches, 'packages' => $packages]);

    }


    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */

    public  function manageAddress(ClientAddressDataTable $dataTable)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' =>  __('cargo::view.manage_address') ,
            ],
        ]);
        $client = Client::where('user_id', auth()->user()->id)->first();
        $client_addresses = ClientAddress::where('client_id',$client->id)->get();

        $data_with = ['client_addresses'=> $client_addresses,'model'=>$client];
        $share_data = array_merge(get_class_vars(ClientAddressDataTable::class), $data_with);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::'.$adminTheme.'.pages.clients.manage_address' , $share_data);

    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public  function manageAddressUpdata(Request $request) {

        ClientAddress::where('client_id',$request->client_id)->update(['is_default'=>0]);
        ClientAddress::where('id', $request->address_id )->update(['is_default'=>1]);


        return redirect()->route('clients.manage-address')->with(['message_alert' => __('cargo::messages.update')]);
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(ClientRequest $request, $id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = Client::findOrFail($id);

        $data = $request->only([ 'state_id' , 'area_id' , 'payment_details' , 'def_mile_cost','def_return_mile_cost','def_mile_cost_gram','def_return_mile_cost_gram','def_return_cost_gram','def_insurance_gram','def_tax_gram','def_shipping_cost_gram','def_return_cost','def_insurance','def_tax','def_shipping_cost','supply_cost','pickup_cost','how_know_us','follow_up_mobile','follow_up_country_code' , 'follow_up_name','name', 'email', 'password', 'responsible_mobile','country_code' ,'responsible_name','national_id','branch_id','address']);

        $Userdata['name']     = $data['name'];
        $Userdata['email']    = $data['email'];

        if(isset($data['password'])){
            $Userdata['password'] = $data['password'];
        }

        $userRegistrationHelper = new UserRegistrationHelper($client->user_id);
		$response = $userRegistrationHelper->NewUser($Userdata);
        if(!$response['success']){
            throw new \Exception($response['error_msg']);
        }

        $data['updated_by'] = auth()->check() ? auth()->id() : null;
        unset($data['password']);
        unset($data['address']);

        $client->fill($data);
        if (!$client->save()){
            throw new \Exception();
        }

        if (isset($request->package_id)) {
            if (!empty($request->package_id)) {
                foreach ($request->package_id as $key => $package) {
                    $client_package = ClientPackage::where('client_id',$client->id)->where('package_id' , $package)->first();
                    if($client_package){
                        $client_package->cost = $request->package_extra[$key];
                    }else{
                        $client_package = new ClientPackage();
                        $client_package->client_id    = $client->id;
                        $client_package->package_id   = $package;
                        $client_package->name = Package::select('name')->where('id',$package)->first();
                        $client_package->name = $client_package->name->name;
                        $client_package->cost = $request->package_extra[$key];
                    }
                    if (!$client_package->save()) {
                        throw new \Exception();
                    }
                }
            }
        }

        $client_addresses = ClientAddress::where('client_id',$client->id)->get();
        if (isset($request->address) && !empty($request->address) ) {


            if (!is_array($request->address)){
                $address = $request->address;
                $request->address = array($address);
            }

            foreach ($request->address as $address) {

                if(isset($address['address']) && $address['address'] != null )
                {
                    $client_address = new ClientAddress();
                    $client_address->fill($address);
                    $client_address->client_id = $client->id;

                    if (!$client_address->save()) {
                        throw new \Exception();
                    }
                }
            }
        }
        $client_addresses->each->delete();

        $client->syncFromMediaLibraryRequest($request->image)->toMediaCollection('avatar');
        return redirect()->back()->with(['message_alert' => __('cargo::messages.saved')]);
    }

    public function ajaxGetClientAddresses(Request $request)
    {
        $client_id = $request->client_id;
        $addresses = ClientAddress::where('client_id', $client_id)->get();
        return response()->json($addresses);
    }


    public function newAddressStore(ClientAddressRequest $request) {

        foreach ($request->address as $key => $item) {
            $new_item = new ClientAddress ;
            $new_item->client_id = $request->client_id;
            $new_item = $new_item->fill($item);
            if(!$new_item->save()) { throw new \Exception(); }
        }


        DB::commit();
        return redirect()->route('clients.manage-address')->with(['message_alert' => __('cargo::messages.saved')]);
    }

    public function addNewAddress(Request  $request , $calc = false , $return_view = false)
    {
        $client_address = new ClientAddress();
        $client_address->client_id                 = $request->client_id;
        $client_address->address                   = $request->address;
        $client_address->country_id                = $request->country;
        $client_address->state_id                  = $request->state;

        if(isset($request->area)){
            $client_address->area_id               = $request->area;
        }

        $googleSettings = resolve(\app\Models\GoogleSettings::class)->toArray();
        $googleMap = json_decode($googleSettings['google_map'], true);
        if($googleMap){
            $client_address->client_street_address_map = $request->client_street_address_map ?? '';
            $client_address->client_lat                = $request->client_lat ?? '';
            $client_address->client_lng                = $request->client_lng ?? '';
            $client_address->client_url                = $request->client_url ?? '';
        }

        if (!$client_address->save()) {
            throw new \Exception();
        }

        if($calc)
        {
            return $client_address;
        }

        $client_id  = $request->client_id;

        if($return_view)
        {
            $addresses = ClientAddress::where('client_id', $client_id)->where('is_archived',0)->orderBy('id','DESC')->paginate(15);
            return $addresses;
        }
        $addresses = ClientAddress::where('client_id', $client_id)->where('is_archived',0)->get();
        return response()->json($addresses);
    }

    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = Client::findOrFail($id);
        $client_addresses = ClientAddress::where('client_id',$client->id)->get();
        $client_addresses->each->delete();
        User::destroy($client->user_id);
        Client::destroy($id);
        return response()->json(['message' => __('cargo::messages.deleted')]);
    }


    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function addressDelete($id)
    {
        try {
            $Client_address = ClientAddress::find($id);
            if (!$Client_address){
                return redirect()->route('clients.manage-address')->with(['message_alert' => __('cargo::messages.multi_deleted_failed')]);
            }
            $Client_address->delete();
            return redirect()->route('clients.manage-address')->with(['message_alert' => __('cargo::messages.deleted')]);
        } catch (\Exception $ex) {
            return redirect()->route('clients.manage-address')->with(['message_alert' => __('cargo::messages.something_wrong')]);
        }
    }


    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function addressEdit($id){

        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.address'),
            ],
        ]);

        $model = ClientAddress::findOrFail($id);
        $client = Client::where('user_id', auth()->user()->id)->first();

        if (!$model)
            return redirect()->route('clients.manage-address')->with(['message_alert' => __('cargo::messages.multi_deleted_failed')]);

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::'.$adminTheme.'.pages.clients.edit_address')->with(['model'=>$model , 'client'=>$client]);

    }


    public function addressUpdata(Request $request) {

        $data = $request->except('_token', 'id');
        ClientAddress::where('id', $request->id)
        ->update(
            $data
        );
        return redirect()->route('clients.manage-address')->with(['message_alert' => __('cargo::messages.update')]);

    }


    /**
     * Remove multi user from database.
     * @param Request $request
     * @return Renderable
     */
    public function multiDestroy(Request $request)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $ids = $request->ids;
        $clients_user_ids = Client::whereIn('id',$ids)->pluck('user_id');
        foreach($ids as $id){
            $client_addresses = ClientAddress::where('client_id',$id)->get();
            $client_addresses->each->delete();
        }
        User::destroy($clients_user_ids);
        Client::destroy($ids);
        return response()->json(['message' => __('cargo::messages.multi_deleted')]);
    }

    public function addNewAddressAPI(Request $request)
    {
        try{
            $request->validate([
                'client_id' => 'nullable',
                'address'   => 'required',
                'country'   => 'required',
                'state'     => 'required',
                'area'      => 'required',
            ]);

            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if($user){
                //amgad
               $client_id = Client::where('user_id', $user->id)->first()->id;
               $requestData = $request->merge(['client_id' => $client_id]);
                $addresses = $this->addNewAddress($requestData);
                return $addresses;
            }else{
                return response()->json(['message' => 'Not Authorized asd']);
            }
        }catch(\Exception $e){
			DB::rollback();
			print_r($e->getMessage());
			exit;
		}
    }

    public function getAddresses(Request $request)
    {
        try{
            if($request->is('api/*')){

                $apihelper = new ApiHelper();
                $user = $apihelper->checkUser($request);

                $request->validate([
                    'client_id' => 'nullable',
                ]);

                if($user){

                    $client_id = Client::where('user_id', $user->id)->first()->id;
                    $addresses = ClientAddress::where('client_id', $client_id )->get();
                    return response()->json($addresses);
                }else{
                    return response()->json(['message' => 'Not Authorized ww']);
                }

            }else {
                $addresses = ClientAddress::where('client_id', Auth::user()->userClient->client_id)->where('is_archived',0)->orderBy('id','DESC')->paginate(15);
                return view('backend.clients.index-addresses',compact(['addresses']));
            }
        }catch(\Exception $e){
			DB::rollback();
			print_r($e->getMessage());
			exit;
		}
    }

    public function clientsReport(ClientsDataTable $dataTable)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.clients_report')
            ]
        ]);
        $data_with = [];
        $share_data = array_merge(get_class_vars(ClientsDataTable::class), $data_with);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::'.$adminTheme.'.pages.clients.report', $share_data);
    }

    public function getOneAddress(Request $request)
    {
        $address_id = $_GET['address_id'];
        $address    = ClientAddress::where('id', $address_id)->get();
        return response()->json($address);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function storeInShipmint(ClientRequest $request)
    {
        $data = $request->only(['def_mile_cost','def_return_mile_cost','def_mile_cost_gram','def_return_mile_cost_gram','def_return_cost_gram','def_insurance_gram','def_tax_gram','def_shipping_cost_gram','def_return_cost','def_insurance','def_tax','def_shipping_cost','supply_cost','pickup_cost','how_know_us','follow_up_mobile','follow_up_country_code' , 'follow_up_name','name', 'email', 'password', 'responsible_mobile', 'country_code' ,'responsible_name','national_id','branch_id','address']);

        $Userdata['name']     = $data['name'];
        $Userdata['email']    = $data['email'];
        $Userdata['password'] = $data['password'];
        $Userdata['role']     = 4;
        $userRegistrationHelper = new UserRegistrationHelper();
		$response = $userRegistrationHelper->NewUser($Userdata);
        if(!$response['success']){
            throw new \Exception($response['error_msg']);
        }

        $data['code']    = 0;
        $data['user_id'] = $response['user']['id'];
        $data['created_by'] = auth()->check() ? auth()->id() : null;
        unset($data['password']);
        unset($data['address']);

        $client = new Client();
        $client->fill($data);
        if (!$client->save()){
            throw new \Exception();
        }
        $client->code = $client->id;
        if (!$client->save()){
            throw new \Exception();
        }

        if (isset($request->package_id)) {
            if (!empty($request->package_id)) {

                foreach ($request->package_id as $key => $package) {
                    $client_package = new ClientPackage();

                    $client_package->client_id    = $client->id;
                    $client_package->package_id   = $package;
                    $client_package->name = Package::select('name')->where('id',$package)->first();
                    $client_package->name = $client_package->name->name;
                    $client_package->cost = $request->package_extra[$key];

                    if (!$client_package->save()) {
                        throw new \Exception();
                    }
                }
            }
        }

        if (isset($request->address) && !empty($request->address) ) {
            foreach ($request->address as $address) {

                if(isset($address['address']) && $address['address'] != null )
                {
                    $client_address = new ClientAddress();
                    $client_address->fill($address);
                    $client_address->client_id = $client->id;

                    if (!$client_address->save()) {
                        throw new \Exception();
                    }
                }
            }
        }

        $client->addFromMediaLibraryRequest($request->image)->toMediaCollection('avatar');
        event(new AddClient($client));

        return response()->json([
            'success' => true,
            'message' => __('cargo::messages.created'),
            'client' => [
                'id' => $client->id,
                'name' => $client->name,
                'responsible_mobile' => $client->responsible_mobile,
                'country_code' => $client->country_code,
            ],
            'client_address' => $client_address ,
        ]);

    }






    public function getFlyersAPI(Request $request)
    {
        try{
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if($user){
                $client_id = Client::where('user_id', $user->id)->first();
                $flyers = Flyer::all();
                return response()->json($flyers);
            }else{
                return response()->json(['message' => 'Not Authorized aa']);
            }
        }catch(\Exception $e){
            return response()->json(['message' => 'Not Authorized ww']);
        }
    }

    public function getClientFlyersOrdersAPI(Request $request)
    {
        try{
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if($user){
                $client_id = Client::where('user_id', $user->id)->first();
                $flyers = FlyerOrder::where('client_id', $client_id->id)->get();


                return response()->json(['success' => true, 'data' => FlyersOrdersResource::collection($flyers) ]);
            }else{
                return response()->json(['message' => 'Not Authorized sss']);
            }
        }catch(\Exception $e){
            return response()->json(['message' => 'Not Authorized www']);
        }
    }




    public function addFlyerOrderAPI(Request $request)
    {
        request()->validate([
            'flyer_id' => 'required',
            'qty' => 'required',
            'size' => 'required',
        ]);

        try{
            $apihelper = new ApiHelper();
            $user = $apihelper->checkUser($request);

            if($user){
                $client = Client::where('user_id', $user->id)->first();

                $flyer = Flyer::find($request->flyer_id);

                $price = $flyer->price * $request->qty;
                FlyerOrder::create([
                    'client_id' => $client->id,
                    'flyer_id' => $request->flyer_id,
                    'qty' => $request->qty,
                    'price' => $price,
                    'size' => $request->size,
                ]);

                return response()->json(['success' => true , 'message' => 'order has been added successfully']);
            }else{
                return response()->json([ 'success' => false ,  'message' => 'Not Authorized sss']);
            }
        }catch(\Exception $e){
            return response()->json(['success' => false ,'message' => 'Not Authorized qqq']);
        }
    }

    /**
     * Get client-specific state costs via AJAX
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ajaxGetClientStateCosts(Request $request)
    {
        try {
            $clientId = $request->get('client_id');
            $stateId = $request->get('state_id');

            \Log::info('Getting client state costs', [
                'client_id' => $clientId,
                'state_id' => $stateId
            ]);

            if (!$stateId) {
                return response()->json([
                    'success' => false,
                    'message' => 'State ID is required'
                ]);
            }

            // Get default state costs
            $state = State::find($stateId);
            if (!$state) {
                return response()->json([
                    'success' => false,
                    'message' => 'State not found'
                ]);
            }

            $defaultCosts = [
                'def_shipping_cost' => $state->def_shipping_cost ?? 0,
                'def_return_cost' => $state->def_return_cost ?? 0,
                'def_shipping_cost_gram' => $state->def_shipping_cost_gram ?? 0,
                'def_return_cost_gram' => $state->def_return_cost_gram ?? 0,
            ];

            // Get client-specific costs if client exists
            $clientCosts = null;
            if ($clientId && $clientId != '0') {
                $clientStateCost = ClientStateCost::where('client_id', $clientId)
                                                 ->where('state_id', $stateId)
                                                 ->first();

                if ($clientStateCost) {
                    $clientCosts = [
                        'def_shipping_cost' => $clientStateCost->def_shipping_cost,
                        'def_return_cost' => $clientStateCost->def_return_cost,
                        'def_shipping_cost_gram' => $clientStateCost->def_shipping_cost_gram,
                        'def_return_cost_gram' => $clientStateCost->def_return_cost_gram,
                    ];

                    \Log::info('Found client-specific costs', $clientCosts);
                } else {
                    \Log::info('No client-specific costs found, using defaults');
                }
            }

            return response()->json([
                'success' => true,
                'costs' => $clientCosts,
                'default_costs' => $defaultCosts,
                'state_name' => $state->name ?? 'Unknown'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error getting client state costs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error retrieving client state costs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Save client-specific state costs via AJAX
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ajaxSaveClientStateCosts(Request $request)
    {
        try {
            $clientId = $request->get('client_id');
            $stateId = $request->get('state_id');
            $costs = $request->get('costs');

            \Log::info('Saving client state costs', [
                'client_id' => $clientId,
                'state_id' => $stateId,
                'costs' => $costs
            ]);

            if (!$clientId || $clientId == '0') {
                return response()->json([
                    'success' => false,
                    'message' => 'Client ID is required'
                ]);
            }

            if (!$stateId) {
                return response()->json([
                    'success' => false,
                    'message' => 'State ID is required'
                ]);
            }

            // Validate client exists
            $client = Client::find($clientId);
            if (!$client) {
                return response()->json([
                    'success' => false,
                    'message' => 'Client not found'
                ]);
            }

            // Validate state exists
            $state = State::find($stateId);
            if (!$state) {
                return response()->json([
                    'success' => false,
                    'message' => 'State not found'
                ]);
            }

            // Save or update client state costs
            $clientStateCost = ClientStateCost::setClientStateCosts($clientId, $stateId, $costs);

            \Log::info('Client state costs saved', [
                'id' => $clientStateCost->id,
                'saved_costs' => [
                    'def_shipping_cost' => $clientStateCost->def_shipping_cost,
                    'def_return_cost' => $clientStateCost->def_return_cost,
                    'def_shipping_cost_gram' => $clientStateCost->def_shipping_cost_gram,
                    'def_return_cost_gram' => $clientStateCost->def_return_cost_gram,
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Client state costs saved successfully',
                'costs' => [
                    'def_shipping_cost' => $clientStateCost->def_shipping_cost,
                    'def_return_cost' => $clientStateCost->def_return_cost,
                    'def_shipping_cost_gram' => $clientStateCost->def_shipping_cost_gram,
                    'def_return_cost_gram' => $clientStateCost->def_return_cost_gram,
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error saving client state costs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error saving client state costs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove client-specific state costs via AJAX
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ajaxRemoveClientStateCosts(Request $request)
    {
        try {
            $clientId = $request->get('client_id');
            $stateId = $request->get('state_id');

            if (!$clientId || $clientId == '0') {
                return response()->json([
                    'success' => false,
                    'message' => 'Client ID is required'
                ]);
            }

            if (!$stateId) {
                return response()->json([
                    'success' => false,
                    'message' => 'State ID is required'
                ]);
            }

            // Remove client-specific costs
            $deleted = ClientStateCost::deleteClientStateCosts($clientId, $stateId);

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'Client state costs removed successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No custom costs found to remove'
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error removing client state costs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Normalize phone number for client registration
     * @param string $phone
     * @return string
     */
    private function normalizePhoneForClient($phone)
    {
        if (empty($phone)) {
            return $phone;
        }

        // Remove all non-numeric characters except +
        $cleanPhone = preg_replace('/[^0-9+]/', '', $phone);

        // Handle old data patterns like "************"
        if (strlen($cleanPhone) >= 7) {
            // Remove + if present for processing
            $numericOnly = str_replace('+', '', $cleanPhone);

            // If it's already a valid Egyptian mobile format, return as is
            if (preg_match('/^01[0125][0-9]{8}$/', $numericOnly)) {
                return $numericOnly;
            }

            // If it's 10 digits and doesn't start with 0, try to make it valid
            if (strlen($numericOnly) == 10 && substr($numericOnly, 0, 1) !== '0') {
                // For old data like "1111100000", convert to "01111100000"
                return '0' . $numericOnly;
            }

            // If it's 11 digits and starts with 0, return as is
            if (strlen($numericOnly) == 11 && substr($numericOnly, 0, 1) === '0') {
                return $numericOnly;
            }

            // If it starts with country code (20), remove it and add 0
            if (substr($numericOnly, 0, 2) === '20' && strlen($numericOnly) >= 12) {
                $phoneWithoutCountryCode = substr($numericOnly, 2);
                if (strlen($phoneWithoutCountryCode) == 10) {
                    return '0' . $phoneWithoutCountryCode;
                }
            }

            return $cleanPhone;
        }

        // Return original if too short
        return $phone;
    }

}
